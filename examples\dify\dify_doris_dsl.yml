app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: agent-chat
  name: doris
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/deepseek:0.0.5@21408d5c48cd9f18d66b08883d0999fe89e6d049c891324c2229dea23b9665d5
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: junjiem/mcp_sse:0.2.1@53cc613667fcf91dd7208dd5f6d2c8df3c7ff0af8b79e8f3c0a430f1b39bda4c
kind: app
model_config:
  agent_mode:
    enabled: true
    max_iteration: 10
    prompt: null
    strategy: function_call
    tools:
    - enabled: true
      isDeleted: false
      notAuthor: false
      provider_id: junjiem/mcp_sse/mcp_sse
      provider_name: junjiem/mcp_sse/mcp_sse
      provider_type: builtin
      tool_label: 获取 MCP 工具列表
      tool_name: mcp_sse_list_tools
      tool_parameters:
        prompts_as_tools: 1
        resources_as_tools: 1
        servers_config: null
    - enabled: true
      isDeleted: false
      notAuthor: false
      provider_id: junjiem/mcp_sse/mcp_sse
      provider_name: junjiem/mcp_sse/mcp_sse
      provider_type: builtin
      tool_label: 调用 MCP 工具
      tool_name: mcp_sse_call_tool
      tool_parameters:
        arguments: ''
        prompts_as_tools: ''
        resources_as_tools: ''
        servers_config: ''
        tool_name: ''
  annotation_reply:
    enabled: false
  chat_prompt_config: {}
  completion_prompt_config: {}
  dataset_configs:
    datasets:
      datasets: []
    reranking_enable: true
    reranking_mode: reranking_model
    reranking_model:
      reranking_model_name: ''
      reranking_provider_name: ''
    retrieval_model: multiple
    top_k: 4
  dataset_query_variable: ''
  external_data_tools: []
  file_upload:
    allowed_file_extensions:
    - .JPG
    - .JPEG
    - .PNG
    - .GIF
    - .WEBP
    - .SVG
    - .MP4
    - .MOV
    - .MPEG
    - .WEBM
    allowed_file_types: []
    allowed_file_upload_methods:
    - remote_url
    - local_file
    enabled: false
    image:
      detail: high
      enabled: false
      number_limits: 3
      transfer_methods:
      - remote_url
      - local_file
    number_limits: 3
  model:
    completion_params:
      stop: []
    mode: chat
    name: deepseek-chat
    provider: langgenius/deepseek/deepseek
  more_like_this:
    enabled: false
  opening_statement: ''
  pre_prompt: "<instruction>\nUse MCP tools to complete tasks as much as possible.\
    \ Carefully read the annotations, method names, and parameter descriptions of\
    \ each tool. Please follow these steps:\n1. Analyze the user's question and match\
    \ the most appropriate tool.\n2. Use tool names and parameters exactly as defined;\
    \ do not invent new ones.\n3. Pass parameters in the required JSON format.\n4.\
    \ When calling tools, use:\n   {\"mcp_sse_call_tool\": {\"tool_name\": \"<tool_name>\"\
    , \"arguments\": \"{}\"}}\n5. Output plain text only—no XML tags.\n<input>\nUser\
    \ question: user_query\n</input>\n<output>\nReturn tool results or a final answer,\
    \ including analysis.\n</output>\n</instruction>"
  prompt_type: simple
  retriever_resource:
    enabled: true
  sensitive_word_avoidance:
    configs: []
    enabled: false
    type: ''
  speech_to_text:
    enabled: false
  suggested_questions: []
  suggested_questions_after_answer:
    enabled: false
  text_to_speech:
    enabled: false
    language: ''
    voice: ''
  user_input_form: []
version: 0.3.0
