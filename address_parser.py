import pandas as pd
import re
import os

def parse_address(address):
    """解析单个地址，返回多条记录"""
    results = []
    
    # 识别园区名称
    park_patterns = ['兴智科技园', '红枫科技园', '汇智科技园']
    park = None
    for pattern in park_patterns:
        if pattern in address:
            park = pattern
            break
    
    # 如果没找到预定义园区，尝试识别其他"xx科技园"模式
    if not park:
        park_match = re.search(r'([^\s]+科技园)', address)
        if park_match:
            park = park_match.group(1)
    
    # 识别栋数
    building_match = re.search(r'([A-Z][0-9]?(?:栋)?|[A-Z]座)', address)
    building = building_match.group(1) if building_match else None
    
    # 识别房间号和楼层
    # 匹配格式如: 901室, 911室, 1209, 1510室, 第二层, 第五层, 3F-5F等
    room_matches = re.findall(r'(\d+(?:室|号)?|\d+F-\d+F|第[一二三四五六七八九十]+[层楼]|\d+[层楼])', address)
    
    # 如果没有找到房间号
    if not room_matches:
        results.append({
            'original_address': address,
            'park': park,
            'building': building,
            'room': None
        })
    else:
        # 对于每个找到的房间号创建一条记录
        for room in room_matches:
            results.append({
                'original_address': address,
                'park': park,
                'building': building,
                'room': room
            })
    
    return results

def parse_csv_file(file_path):
    """解析CSV文件中的地址"""
    # 尝试不同的编码方式读取CSV
    encodings = ['utf-8', 'gbk', 'gb18030', 'utf-16']
    df = None
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            break
        except Exception as e:
            print(f"尝试使用 {encoding} 编码失败: {e}")
    
    if df is None:
        raise ValueError("无法读取CSV文件，请检查文件编码")
    
    # 获取第一列的列名
    address_column = df.columns[0]
    
    # 解析所有地址
    all_results = []
    for address in df[address_column]:
        if isinstance(address, str) and len(address.strip()) > 0:
            parsed_results = parse_address(address)
            all_results.extend(parsed_results)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(all_results)
    
    # 保存结果
    output_file = os.path.splitext(file_path)[0] + "_parsed.csv"
    result_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"解析完成，共 {len(result_df)} 条记录，已保存到 {output_file}")
    return result_df

if __name__ == "__main__":
    # 解析CSV文件
    file_path = "ddress_parse_test0704.csv"
    parsed_df = parse_csv_file(file_path)
    
    # 显示前10条结果
    print("\n解析结果示例:")
    print(parsed_df.head(10))