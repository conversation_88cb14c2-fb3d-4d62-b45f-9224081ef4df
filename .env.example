# Doris MCP Server Configuration
# Copy this file to .env and modify the values according to your environment

# =============================================================================
# Database Configuration
# =============================================================================

# Doris FE connection settings
DORIS_HOST=localhost
DORIS_PORT=9030
DORIS_USER=root
DORIS_PASSWORD=
DORIS_DATABASE=information_schema

# Doris FE HTTP API port
DORIS_FE_HTTP_PORT=8030

# BE nodes configuration for external access
# If DORIS_BE_HOSTS is empty, will use "show backends" to get BE nodes automatically
# Format: comma-separated list of BE host addresses
# Example: DORIS_BE_HOSTS=*************,*************,*************
DORIS_BE_HOSTS=

# BE webserver port for HTTP APIs (memory tracker, metrics, etc.)
DORIS_BE_WEBSERVER_PORT=8040

# =============================================================================
# Connection Pool Configuration
# =============================================================================

DORIS_MAX_CONNECTIONS=20
DORIS_CONNECTION_TIMEOUT=30
DORIS_HEALTH_CHECK_INTERVAL=60
DORIS_MAX_CONNECTION_AGE=3600

# =============================================================================
# Profile And Explain Max Data Size
# =============================================================================
MAX_RESPONSE_CONTENT_SIZE=4096

# =============================================================================
# Security Configuration
# =============================================================================

ENABLE_SECURITY_CHECK=true
BLOCKED_KEYWORDS="DROP,TRUNCATE,DELETE,SHUTDOWN,INSERT,UPDATE,CREATE,ALTER,GRANT,REVOKE,KILL"
AUTH_TYPE=token
TOKEN_SECRET=your_secret_key_here
TOKEN_EXPIRY=3600
MAX_RESULT_ROWS=10000
MAX_QUERY_COMPLEXITY=100
ENABLE_MASKING=true

# =============================================================================
# Performance Configuration
# =============================================================================

ENABLE_QUERY_CACHE=true
CACHE_TTL=300
MAX_CACHE_SIZE=1000
MAX_CONCURRENT_QUERIES=50
QUERY_TIMEOUT=300

# =============================================================================
# Logging Configuration
# =============================================================================

LOG_LEVEL=INFO
LOG_FILE_PATH=
ENABLE_AUDIT=true
AUDIT_FILE_PATH=

# =============================================================================
# Monitoring Configuration
# =============================================================================

ENABLE_METRICS=true
METRICS_PORT=3001
HEALTH_CHECK_PORT=3002
ENABLE_ALERTS=false
ALERT_WEBHOOK_URL=

# =============================================================================
# Server Configuration
# =============================================================================

SERVER_NAME=doris-mcp-server
SERVER_VERSION=0.4.2
SERVER_PORT=3000
