# Main dependencies - auto-generated from pyproject.toml
# Do not edit this file manually, use 'python generate_requirements.py' to regenerate

# === Core Dependencies ===
mcp>=1.8.0,<2.0.0
aiomysql>=0.2.0
PyMySQL>=1.1.0
asyncio-mqtt>=0.16.0
aiofiles>=23.0.0
aiohttp>=3.9.0
aioredis>=2.0.0
pandas>=2.0.0
numpy>=1.24.0
python-dateutil>=2.8.0
orjson>=3.9.0
pydantic>=2.5.0
pydantic-settings>=2.1.0
toml>=0.10.0
PyYAML>=6.0.0
python-dotenv>=1.0.0
cryptography>=41.0.0
PyJWT>=2.8.0
passlib[bcrypt]>=1.7.0
bcrypt>=4.1.0
sqlparse>=0.4.4
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6
prometheus-client>=0.19.0
structlog>=23.2.0
rich>=13.7.0
httpx>=0.26.0
websockets>=12.0
uvicorn[standard]>=0.25.0
fastapi>=0.108.0
starlette>=0.27.0
click>=8.1.0
typer>=0.9.0
requests>=2.31.0
tqdm>=4.66.0
pytest>=8.4.0
pytest-asyncio>=1.0.0
pytest-cov>=6.1.1

# === Development Dependencies ===
pytest>=7.4.0
pytest-asyncio>=0.23.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
pytest-xdist>=3.5.0
ruff>=0.1.0
black>=23.12.0
isort>=5.13.0
flake8>=7.0.0
mypy>=1.8.0
bandit>=1.7.0
safety>=2.3.0
sphinx>=7.2.0
sphinx-rtd-theme>=2.0.0
myst-parser>=2.0.0
pre-commit>=3.6.0
tox>=4.11.0