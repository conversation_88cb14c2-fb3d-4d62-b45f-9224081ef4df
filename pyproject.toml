# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "doris-mcp-server"
version = "0.4.2"
description = "Enterprise-grade Model Context Protocol (MCP) server implementation for Apache Doris"
authors = [
    {name = "Yijia Su", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "Apache-2.0"}
requires-python = ">=3.12"
keywords = ["doris", "mcp", "model-context-protocol", "database", "analytics"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3.12",
    "Topic :: Database",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Information Analysis",
]

dependencies = [
    # Core MCP dependencies
    "mcp>=1.8.0,<2.0.0",
    # Database drivers
    "aiomysql>=0.2.0",
    "PyMySQL>=1.1.0",
    # Async and utility libraries
    "asyncio-mqtt>=0.16.0",
    "aiofiles>=23.0.0",
    "aiohttp>=3.9.0",
    "aioredis>=2.0.0",
    # Data processing
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "python-dateutil>=2.8.0",
    "orjson>=3.9.0",
    # Configuration and serialization
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "toml>=0.10.0",
    "PyYAML>=6.0.0",
    "python-dotenv>=1.0.0",
    # Security and authentication
    "cryptography>=41.0.0",
    "PyJWT>=2.8.0",
    "passlib[bcrypt]>=1.7.0",
    "bcrypt>=4.1.0",
    "sqlparse>=0.4.4",
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.6",
    # Monitoring and logging
    "prometheus-client>=0.19.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    # HTTP and networking
    "httpx>=0.26.0",
    "websockets>=12.0",
    "uvicorn[standard]>=0.25.0",
    "fastapi>=0.108.0",
    "starlette>=0.27.0",
    # Development utilities
    "click>=8.1.0",
    "typer>=0.9.0",
    "requests>=2.31.0",
    "tqdm>=4.66.0",
    "pytest>=8.4.0",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.1.1",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-xdist>=3.5.0",
    
    # Code quality
    "ruff>=0.1.0",
    "black>=23.12.0",
    "isort>=5.13.0",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "bandit>=1.7.0",
    "safety>=2.3.0",
    
    # Documentation
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=2.0.0",
    "myst-parser>=2.0.0",
    
    # Development tools
    "pre-commit>=3.6.0",
    "tox>=4.11.0",
]

docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=2.0.0",
    "myst-parser>=2.0.0",
    "sphinx-autoapi>=3.0.0",
]

performance = [
    "uvloop>=0.19.0",  # High-performance event loop
    "orjson>=3.9.0",   # Fast JSON serialization
    "cchardet>=2.1.0", # Fast character encoding detection
]

monitoring = [
    "prometheus-client>=0.19.0",
    "grafana-client>=3.5.0",
    "jaeger-client>=4.8.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
]

[project.urls]
Homepage = "https://github.com/apache/doris-mcp-server"
Documentation = "https://doris.apache.org/docs/"
Repository = "https://github.com/apache/doris-mcp-server.git"
Issues = "https://github.com/apache/doris-mcp-server/issues"
Changelog = "https://github.com/apache/doris-mcp-server/blob/main/CHANGELOG.md"

[project.scripts]
doris-mcp-server = "doris_mcp_server.main:main_sync"
doris-mcp-client = "doris_mcp_server.client:main"

[tool.hatch.build.targets.wheel]
packages = ["doris_mcp_server"]

[tool.hatch.build.targets.sdist]
include = [
    "/doris_mcp_server",
    "/README.md",
    "/LICENSE",
]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["doris_mcp_server"]
known_third_party = ["mcp", "aiomysql", "pydantic", "click"]

# MyPy configuration
[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "aiomysql.*",
    "pymysql.*",
    "prometheus_client.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=doris_mcp_server",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

# Coverage configuration
[tool.coverage.run]
source = ["doris_mcp_server"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit security linter configuration
[tool.bandit]
exclude_dirs = ["tests", "build", "dist"]
tests = ["B201", "B301"]
skips = ["B101", "B601"]

# Ruff configuration (modern Python linter)
[tool.ruff]
target-version = "py312"
line-length = 88

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
    "B904",  # raise from err
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"] 

[dependency-groups]
dev = [
    "ruff>=0.11.13",
]
 