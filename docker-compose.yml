# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
version: '3.8'

services:
  # Doris MCP Server
  doris-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: doris-mcp-server
    ports:
      - "3000:3000"  # MCP service port
      - "3001:3001"  # Monitoring metrics port
      - "3002:3002"  # Health check port
    environment:
      # Database configuration
      - DORIS_HOST=doris-fe
      - DORIS_PORT=9030
      - DORIS_USER=root
      - DORIS_PASSWORD=doris123
      - DORIS_DATABASE=test_db
      
      # Connection pool configuration
      - DORIS_MIN_CONNECTIONS=5
      - DORIS_MAX_CONNECTIONS=20
      
      # Security configuration
      - AUTH_TYPE=token
      - TOKEN_SECRET=your_secret_key_here
      - MAX_RESULT_ROWS=10000
      
      # Performance configuration
      - ENABLE_QUERY_CACHE=true
      - MAX_CONCURRENT_QUERIES=50
      
      # Logging configuration
      - LOG_LEVEL=INFO
      - LOG_FILE_PATH=/app/logs/doris-mcp-server.log
      
      # Monitoring configuration
      - ENABLE_METRICS=true
      - METRICS_PORT=8081
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - doris-fe
      - doris-be
    networks:
      - doris-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Apache Doris Frontend
  doris-fe:
    image: apache/doris:2.0.3-fe-x86_64
    container_name: doris-fe
    ports:
      - "8030:8030"  # FE HTTP port
      - "9030:9030"  # FE MySQL port
    environment:
      - FE_SERVERS=fe1:doris-fe:9010
      - FE_ID=1
    volumes:
      - doris-fe-data:/opt/apache-doris/fe/doris-meta
      - doris-fe-log:/opt/apache-doris/fe/log
      - ./doris-config/fe.conf:/opt/apache-doris/fe/conf/fe.conf
    networks:
      - doris-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8030/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Apache Doris Backend
  doris-be:
    image: apache/doris:2.0.3-be-x86_64
    container_name: doris-be
    ports:
      - "8040:8040"  # BE HTTP port
      - "9060:9060"  # BE heartbeat port
    environment:
      - FE_SERVERS=doris-fe:9010
      - BE_ADDR=doris-be:9050
    volumes:
      - doris-be-data:/opt/apache-doris/be/storage
      - doris-be-log:/opt/apache-doris/be/log
      - ./doris-config/be.conf:/opt/apache-doris/be/conf/be.conf
    depends_on:
      - doris-fe
    networks:
      - doris-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8040/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis cache (optional)
  redis:
    image: redis:7-alpine
    container_name: doris-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis-data:/data
    networks:
      - doris-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: doris-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - doris-network
    restart: unless-stopped

  # Grafana visualization
  grafana:
    image: grafana/grafana:latest
    container_name: doris-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - doris-network
    restart: unless-stopped

  # Nginx load balancer
  nginx:
    image: nginx:alpine
    container_name: doris-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - doris-mcp-server
    networks:
      - doris-network
    restart: unless-stopped

volumes:
  doris-fe-data:
    driver: local
  doris-fe-log:
    driver: local
  doris-be-data:
    driver: local
  doris-be-log:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  doris-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16 