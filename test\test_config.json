{"server_endpoints": {"http": {"url": "http://localhost:3000/mcp", "timeout": 30, "headers": {"Content-Type": "application/json"}}, "http_network": {"url": "http://**************:3000/mcp", "timeout": 30, "headers": {"Content-Type": "application/json"}}, "stdio": {"command": "uv", "args": ["run", "python", "-m", "doris_mcp_server.main", "--transport", "stdio"], "timeout": 30, "working_directory": ".."}}, "test_settings": {"default_transport": "http", "retry_attempts": 3, "retry_delay": 1.0, "test_timeout": 60, "enable_performance_tests": true, "enable_security_tests": true}, "test_data": {"sample_queries": ["SELECT 1 as test_value", "SHOW DATABASES", "SELECT COUNT(*) FROM information_schema.tables"], "test_databases": ["test_db", "demo_db"], "test_tables": ["users", "orders", "products"], "auth_tokens": {"valid_token": "valid_token_123", "admin_token": "admin_token_456", "invalid_token": "invalid_token_789"}}, "expected_tools": ["exec_query", "get_db_list", "get_db_table_list", "get_table_schema", "get_table_comment", "get_table_column_comments", "get_table_indexes", "get_recent_audit_logs", "get_catalog_list", "get_sql_explain", "get_sql_profile", "get_table_data_size", "get_monitoring_metrics_info", "get_monitoring_metrics_data", "get_realtime_memory_stats", "get_historical_memory_stats"], "expected_resources": ["database", "table", "view"], "expected_prompts": ["sql_query_assistant", "data_analysis_helper", "schema_explorer"]}